"use client";

import {useState} from "react";
import {But<PERSON>} from "@/components/ui/button";
import {Input} from "@/components/ui/input";
import {ScrollArea} from "@/components/ui/scroll-area";
import {Card, CardContent} from "@/components/ui/card";
import {useDropzone} from "react-dropzone";
import {Upload, X} from "lucide-react";

export default function Home() {
    const [messages, setMessages] = useState([
        {id: 1, text: "Hello! How can I help you today?", sender: "bot"},
    ]);
    const [inputText, setInputText] = useState("");
    const [selectedImage, setSelectedImage] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string | null>(null);
    const handleSend = async () => {
        if (!inputText.trim()) return;

        const userMessage = inputText;
        setMessages((prev) => [
            ...prev,
            {id: prev.length + 1, text: userMessage, sender: "user"},
        ]);
        setInputText("");

        try {
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message: userMessage }),
            });

            if (!response.ok) {
                throw new Error('Failed to get response');
            }

            const data = await response.json();

            setMessages((prev) => [
                ...prev,
                {
                    id: prev.length + 1,
                    text: data.response,
                    sender: "bot",
                },
            ]);
        } catch (error) {
            console.error('Error sending message:', error);
            setMessages((prev) => [
                ...prev,
                {
                    id: prev.length + 1,
                    text: "Sorry, I encountered an error. Please try again.",
                    sender: "bot",
                },
            ]);
        }
    };

    const handleImageUpload = async (file: File) => {
        setSelectedImage(file);

        // Create preview
        const reader = new FileReader();
        reader.onload = (e) => {
            setImagePreview(e.target?.result as string);
        };
        reader.readAsDataURL(file);
    };

    const handleImageSend = async () => {
        if (!selectedImage) return;

        const prompt = inputText.trim() || "What do you see in this image?";

        setMessages((prev) => [
            ...prev,
            {id: prev.length + 1, text: `🖼️ Image: ${prompt}`, sender: "user"},
        ]);
        setInputText("");
        setSelectedImage(null);
        setImagePreview(null);

        try {
            const formData = new FormData();
            formData.append('image', selectedImage);
            formData.append('prompt', prompt);

            const response = await fetch('/api/image', {
                method: 'POST',
                body: formData,
            });

            if (!response.ok) {
                throw new Error('Failed to analyze image');
            }

            const data = await response.json();

            setMessages((prev) => [
                ...prev,
                {
                    id: prev.length + 1,
                    text: data.response,
                    sender: "bot",
                },
            ]);
        } catch (error) {
            console.error('Error analyzing image:', error);
            setMessages((prev) => [
                ...prev,
                {
                    id: prev.length + 1,
                    text: "Sorry, I encountered an error analyzing the image. Please try again.",
                    sender: "bot",
                },
            ]);
        }
    };

    const removeImage = () => {
        setSelectedImage(null);
        setImagePreview(null);
    };

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        accept: {
            'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.bmp', '.svg']
        },
        maxFiles: 1,
        maxSize: 10 * 1024 * 1024, // 10MB limit
        multiple: false,
        onDrop: (acceptedFiles, rejectedFiles) => {
            if (rejectedFiles.length > 0) {
                const rejection = rejectedFiles[0];
                let errorMessage = "File rejected: ";
                if (rejection.errors.some(e => e.code === 'file-too-large')) {
                    errorMessage += "File is too large (max 10MB)";
                } else if (rejection.errors.some(e => e.code === 'file-invalid-type')) {
                    errorMessage += "Invalid file type (only images allowed)";
                } else {
                    errorMessage += rejection.errors[0]?.message || "Unknown error";
                }

                setMessages((prev) => [
                    ...prev,
                    {
                        id: prev.length + 1,
                        text: errorMessage,
                        sender: "bot",
                    },
                ]);
                return;
            }

            if (acceptedFiles.length > 0) {
                handleImageUpload(acceptedFiles[0]);
            }
        },
        onDropRejected: (rejectedFiles) => {
            console.log('Files rejected:', rejectedFiles);
        },
        onError: (error) => {
            console.error('Dropzone error:', error);
            setMessages((prev) => [
                ...prev,
                {
                    id: prev.length + 1,
                    text: "Error uploading file. Please try again.",
                    sender: "bot",
                },
            ]);
        }
    });

    const Dropzone = () => (
        <div className="flex items-center gap-2">
            {imagePreview && (
                <div className="relative group">
                    <img
                        src={imagePreview}
                        alt="Selected image preview"
                        className="w-12 h-12 object-cover rounded border shadow-sm"
                    />
                    <button
                        onClick={removeImage}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs hover:bg-red-600 transition-colors shadow-sm"
                        title="Remove image"
                        aria-label="Remove selected image"
                    >
                        <X size={12} />
                    </button>
                </div>
            )}
            <div
                {...getRootProps()}
                className={`
                    border-2 border-dashed rounded-lg p-2 cursor-pointer transition-all duration-200
                    ${isDragActive
                        ? 'border-primary bg-primary/10 scale-105'
                        : 'border-gray-300 hover:border-primary/50 hover:bg-gray-50'
                    }
                    ${selectedImage ? 'w-auto min-w-[3rem]' : 'w-12 h-12'}
                    flex items-center justify-center
                    focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary
                `}
                title={isDragActive ? "Drop image here" : "Click to upload image or drag and drop"}
                role="button"
                tabIndex={0}
            >
                <input {...getInputProps()} />
                <Upload
                    size={16}
                    className={`transition-colors ${isDragActive ? 'text-primary' : 'text-gray-500'}`}
                />
                {isDragActive && (
                    <span className="ml-2 text-sm text-primary font-medium">Drop here</span>
                )}
            </div>
            {selectedImage && (
                <Button onClick={handleImageSend} size="sm" className="whitespace-nowrap">
                    Analyze Image
                </Button>
            )}
        </div>
    );


    return (
        <div className="flex flex-col h-screen">
            <header className="border-b px-6 py-4">
                <h1 className="text-2xl font-bold tracking-tight">Chat Interface</h1>
            </header>

            <ScrollArea className="flex-1 p-4">
                <div className="flex flex-col gap-4 max-w-3xl mx-auto">
                    {messages.map((message) => (
                        <div
                            key={message.id}
                            className={`flex ${
                                message.sender === "user" ? "justify-end" : "justify-start"
                            }`}
                        >
                            <Card
                                className={`max-w-[70%] ${
                                    message.sender === "user" ? "bg-primary" : "bg-muted"
                                }`}
                            >
                                <CardContent
                                    className={`p-3 ${
                                        message.sender === "user"
                                            ? "text-primary-foreground"
                                            : "text-muted-foreground"
                                    }`}
                                >
                                    {message.text}
                                </CardContent>
                            </Card>
                        </div>
                    ))}
                </div>
            </ScrollArea>

            <footer className="border-t p-4">
                <div className="max-w-3xl mx-auto flex gap-2">
                    <Input
                        value={inputText}
                        onChange={(e) => setInputText(e.target.value)}
                        onKeyPress={(e) => e.key === "Enter" && handleSend()}
                        placeholder="Type a message..."
                        className="flex-1"
                    />
                    <Button onClick={handleSend}>Send</Button>
                    <Dropzone />
                </div>
            </footer>
        </div>
    );
}
