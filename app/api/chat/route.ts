import { NextRequest, NextResponse } from 'next/server';
import { getChatCompletion, ChatMessage } from '@/functions/openai';

export async function POST(request: NextRequest) {
  try {
    const { message } = await request.json();

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required and must be a string' },
        { status: 400 }
      );
    }

    // Create the messages array for OpenAI
    const messages: ChatMessage[] = [
      {
        role: 'system',
        content: 'You are a helpful assistant. Provide concise and helpful responses.'
      },
      {
        role: 'user',
        content: message
      }
    ];

    const response = await getChatCompletion(messages);

    return NextResponse.json({ response });
  } catch (error) {
    console.error('Chat API error:', error);
    return NextResponse.json(
      { error: 'Failed to process chat request' },
      { status: 500 }
    );
  }
}
